const _ = require('lodash');
const async = require('async');
const Joi = require('joi');

const statisticsService = require('../../../services/statisticsService');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');

/**
 * API thống kê các vấn đề nổi bật (Incidents Highlight)
 * POST /api/v1.0/statistics/reports-incidents-highlight
 *
 * Tr<PERSON> về thống kê các vấn đề nổi bật từ các báo cáo có chartTypes chứa "highlight"
 * <PERSON>o gồm tổng số vụ việc theo lĩnh vực, theo khu vực và tỷ lệ thay đổi so với khoảng thời gian trước
 * Dữ liệu này được sử dụng để hiển thị thống kê incidents highlight trên dashboard
 */
module.exports = (req, res) => {
  const userId = req.user.id;
  const {
    timeRange = 'day',
    startDate,
    endDate
  } = req.body;

  let result;

  /**
   * Validate tham số đầu vào
   */
  const validateParams = (next) => {
    const schema = Joi.object({
      timeRange: Joi.string().valid('day', 'week', 'month', 'custom').default('day'),
      startDate: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).when('timeRange', {
        is: 'custom',
        then: Joi.required(),
        otherwise: Joi.optional()
      }),
      endDate: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).when('timeRange', {
        is: 'custom',
        then: Joi.required(),
        otherwise: Joi.optional()
      })
    });

    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    next();
  };

  /**
   * Lấy thống kê các vấn đề nổi bật từ service
   */
  const getIncidentsHighlightStats = (next) => {
    try {
      statisticsService.getIncidentsHighlightStats({
        timeRange,
        startDate,
        endDate,
        userId
      })
        .then((serviceResult) => {
          if (!serviceResult || !serviceResult.success) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: serviceResult.message || MESSAGES.SYSTEM.ERROR
            });
          }

          result = serviceResult;
          next();
        })
        .catch((err) => {
          next(err);
        });
    } catch (error) {
      next(error);
    }
  };

  /**
   * Ghi log và trả về kết quả
   */
  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      data: result.data
    });

    // Ghi log hoạt động
    if (SystemLogModel) {
      SystemLogModel.create({
        user: userId,
        action: 'get_incidents_highlight_stats',
        description: 'Xem thống kê các vấn đề nổi bật',
        data: req.body,
        updatedData: {
          totalIncidents: result.data?.summary?.totalIncidents || 0,
          totalReports: result.data?.summary?.totalReports || 0,
          jobTypeCount: result.data?.byJobType?.length || 0,
          areaCount: result.data?.byArea?.length || 0,
          timeRange: result.data?.period?.type,
          hasChangeRate: !!result.data?.changeRate
        }
      }, () => {});
    }
  };

  // Thực thi waterfall
  async.waterfall([
    validateParams,
    getIncidentsHighlightStats,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger && logger.logError([err], req.originalUrl, req.body);
      MailUtil && MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    // Xử lý lỗi hệ thống
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};
