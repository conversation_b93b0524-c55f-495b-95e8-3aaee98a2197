const _ = require('lodash');
const async = require('async');
const Joi = require('joi');

const statisticsService = require('../../../services/statisticsService');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');

/**
 * API thống kê báo cáo theo thời gian
 * POST /api/v1.0/statistics/reports-timeline
 *
 * Trả về thống kê báo cáo theo timeline (thời gian)
 * <PERSON>o gồm số lượng báo cáo theo từng khoảng thời gian, trend và peak time
 * Dữ liệu này được sử dụng để hiển thị biểu đồ timeline trên dashboard
 */
module.exports = (req, res) => {
  const userId = req.user.id;
  const {
    timeRange = 'week',
    startDate,
    endDate,
    groupBy = 'day'
  } = req.body;

  let result;

  /**
   * Validate tham số đầu vào
   */
  const validateParams = (next) => {
    const schema = Joi.object({
      timeRange: Joi.string().valid('day', 'week', 'month', 'custom').default('week'),
      startDate: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).when('timeRange', {
        is: 'custom',
        then: Joi.required(),
        otherwise: Joi.optional()
      }),
      endDate: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).when('timeRange', {
        is: 'custom',
        then: Joi.required(),
        otherwise: Joi.optional()
      }),
      groupBy: Joi.string().valid('hour', 'day', 'week', 'month').default('day')
    });

    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    next();
  };

  /**
   * Lấy thống kê báo cáo theo thời gian từ service
   */
  const getReportsTimelineStats = (next) => {
    try {
      statisticsService.getReportsTimelineStats({
        timeRange,
        startDate,
        endDate,
        groupBy,
        userId
      })
        .then((serviceResult) => {
          if (!serviceResult || !serviceResult.success) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: serviceResult.message || MESSAGES.SYSTEM.ERROR
            });
          }

          result = serviceResult;
          next();
        })
        .catch((err) => {
          next(err);
        });
    } catch (error) {
      next(error);
    }
  };

  /**
   * Ghi log và trả về kết quả
   */
  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      // message: result.message,
      data: result.data
    });

    // Ghi log hoạt động
    if (global.SystemLogModel) {
      global.SystemLogModel.create({
        user: userId,
        action: 'get_reports_timeline_stats',
        description: 'Xem thống kê báo cáo theo thời gian',
        data: req.body,
        updatedData: {
          totalSlots: result.data?.summary?.totalSlots || 0,
          totalReports: result.data?.summary?.totalReports || 0,
          averageReportsPerSlot: result.data?.summary?.averageReportsPerSlot || 0,
          trend: result.data?.summary?.trend,
          timeRange: result.data?.period?.type,
          groupBy: result.data?.groupBy
        }
      }, () => {});
    }
  };

  // Thực thi waterfall
  async.waterfall([
    validateParams,
    getReportsTimelineStats,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      global.logger && global.logger.logError([err], req.originalUrl, req.body);
      global.MailUtil && global.MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    // Xử lý lỗi hệ thống
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};
